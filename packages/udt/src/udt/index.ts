import { ccc } from "@ckb-ccc/core";
import { ssri } from "@ckb-ccc/ssri";

/**
 * Represents a User Defined Token (UDT) script compliant with the SSRI protocol.
 *
 * This class provides a comprehensive implementation for interacting with User Defined Tokens,
 * supporting various token operations such as querying metadata, checking balances, and performing transfers.
 * It supports both SSRI-compliant UDTs and legacy sUDT/xUDT standard tokens.
 *
 * @public
 * @category Blockchain
 * @category Token
 */
export class Udt extends ssri.Trait {
  public readonly script: ccc.Script;

  /**
   * Constructs a new UDT (User Defined Token) script instance.
   * By default it is a SSRI-compliant UDT. By providing `xudtType`, it is compatible with the legacy xUDT.
   *
   * @param executor - The SSRI executor instance.
   * @param code - The script code cell of the UDT.
   * @param script - The type script of the UDT.
   * @example
   * ```typescript
   * const udt = new Udt(executor, code, script);
   * ```
   */
  constructor(
    code: ccc.OutPointLike,
    script: ccc.ScriptLike,
    config?: {
      executor?: ssri.Executor | null;
    } | null,
  ) {
    super(code, config?.executor);
    this.script = ccc.Script.from(script);
  }

  /**
   * Retrieves the human-readable name of the User Defined Token.
   * This method queries the UDT script to get the token's display name,
   * which is typically used in user interfaces and wallets.
   *
   * @param context - Optional script execution context for additional parameters
   * @returns A promise resolving to an ExecutorResponse containing the token's name,
   *          or undefined if the name is not available or the script doesn't support this method
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   * const nameResponse = await udt.name();
   * if (nameResponse.res) {
   *   console.log(`Token name: ${nameResponse.res}`);
   * }
   * ```
   */
  async name(
    context?: ssri.ContextScript,
  ): Promise<ssri.ExecutorResponse<string | undefined>> {
    if (this.executor) {
      const res = await this.executor.runScriptTry(this.code, "UDT.name", [], {
        script: this.script,
        ...context,
      });
      if (res) {
        return res.map((res) => ccc.bytesTo(res, "utf8"));
      }
    }

    return ssri.ExecutorResponse.new(undefined);
  }

  /**
   * Retrieves the symbol (ticker) of the User Defined Token.
   * The symbol is typically a short abbreviation used to identify the token,
   * similar to stock ticker symbols (e.g., "BTC", "ETH", "USDT").
   *
   * @param context - Optional script execution context for additional parameters
   * @returns A promise resolving to an ExecutorResponse containing the token's symbol,
   *          or undefined if the symbol is not available or the script doesn't support this method
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   * const symbolResponse = await udt.symbol();
   * if (symbolResponse.res) {
   *   console.log(`Token symbol: ${symbolResponse.res}`);
   * }
   * ```
   */
  async symbol(
    context?: ssri.ContextScript,
  ): Promise<ssri.ExecutorResponse<string | undefined>> {
    if (this.executor) {
      const res = await this.executor.runScriptTry(
        this.code,
        "UDT.symbol",
        [],
        {
          script: this.script,
          ...context,
        },
      );
      if (res) {
        return res.map((res) => ccc.bytesTo(res, "utf8"));
      }
    }

    return ssri.ExecutorResponse.new(undefined);
  }

  /**
   * Retrieves the number of decimal places for the User Defined Token.
   * This value determines how the token amount should be displayed and interpreted.
   * For example, if decimals is 8, then a balance of 100000000 represents 1.0 tokens.
   *
   * @param context - Optional script execution context for additional parameters
   * @returns A promise resolving to an ExecutorResponse containing the number of decimals,
   *          or undefined if decimals are not specified or the script doesn't support this method
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   * const decimalsResponse = await udt.decimals();
   * if (decimalsResponse.res !== undefined) {
   *   console.log(`Token decimals: ${decimalsResponse.res}`);
   *   // Convert raw amount to human-readable format
   *   const humanReadable = rawAmount / (10 ** Number(decimalsResponse.res));
   * }
   * ```
   */
  async decimals(
    context?: ssri.ContextScript,
  ): Promise<ssri.ExecutorResponse<ccc.Num | undefined>> {
    if (this.executor) {
      const res = await this.executor.runScriptTry(
        this.code,
        "UDT.decimals",
        [],
        {
          script: this.script,
          ...context,
        },
      );
      if (res) {
        return res.map((res) => ccc.numFromBytes(res));
      }
    }

    return ssri.ExecutorResponse.new(undefined);
  }

  /**
   * Retrieves the icon URL or data URI for the User Defined Token.
   * This can be used to display a visual representation of the token in user interfaces.
   * The returned value may be a URL pointing to an image file or a data URI containing
   * the image data directly.
   *
   * @param context - Optional script execution context for additional parameters
   * @returns A promise resolving to an ExecutorResponse containing the icon URL/data,
   *          or undefined if no icon is available or the script doesn't support this method
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   * const iconResponse = await udt.icon();
   * if (iconResponse.res) {
   *   // Use the icon in UI
   *   const imgElement = document.createElement('img');
   *   imgElement.src = iconResponse.res;
   * }
   * ```
   */
  async icon(
    context?: ssri.ContextScript,
  ): Promise<ssri.ExecutorResponse<string | undefined>> {
    if (this.executor) {
      const res = await this.executor.runScriptTry(this.code, "UDT.icon", [], {
        script: this.script,
        ...context,
      });
      if (res) {
        return res.map((res) => ccc.bytesTo(res, "utf8"));
      }
    }

    return ssri.ExecutorResponse.new(undefined);
  }

  /**
   * Transfers UDT to specified addresses.
   * @param tx - Transfer on the basis of an existing transaction to achieve combined actions. If not provided, a new transaction will be created.
   * @param transfers - The array of transfers.
   * @param transfers.to - The receiver of token.
   * @param transfers.amount - The amount of token to the receiver.
   * @returns The transaction result.
   * @tag Mutation - This method represents a mutation of the onchain state and will return a transaction object.
   * @example
   * ```typescript
   * const { script: change } = await signer.getRecommendedAddressObj();
   * const { script: to } = await ccc.Address.fromString(receiver, signer.client);
   *
   * const udt = new Udt(
   *   {
   *     txHash: "0x4e2e832e0b1e7b5994681b621b00c1e65f577ee4b440ef95fa07db9bb3d50269",
   *     index: 0,
   *   },
   *   {
   *     codeHash: "0xcc9dc33ef234e14bc788c43a4848556a5fb16401a04662fc55db9bb201987037",
   *     hashType: "type",
   *     args: "0x71fd1985b2971a9903e4d8ed0d59e6710166985217ca0681437883837b86162f"
   *   },
   * );
   *
   * const { res: tx } = await udtTrait.transfer(
   *   signer,
   *   [{ to, amount: 100 }],
   * );
   *
   * const completedTx = udt.completeUdtBy(tx, signer);
   * await completedTx.completeInputsByCapacity(signer);
   * await completedTx.completeFeeBy(signer);
   * const transferTxHash = await signer.sendTransaction(completedTx);
   * ```
   */
  async transfer(
    signer: ccc.Signer,
    transfers: {
      to: ccc.ScriptLike;
      amount: ccc.NumLike;
    }[],
    tx?: ccc.TransactionLike | null,
  ): Promise<ssri.ExecutorResponse<ccc.Transaction>> {
    let resTx;
    if (this.executor) {
      const txReq = ccc.Transaction.from(tx ?? {});
      await txReq.completeInputsAtLeastOne(signer);

      const res = await this.executor.runScriptTry(
        this.code,
        "UDT.transfer",
        [
          txReq.toBytes(),
          ccc.ScriptVec.encode(transfers.map(({ to }) => to)),
          ccc.mol.Uint128Vec.encode(transfers.map(({ amount }) => amount)),
        ],
        {
          script: this.script,
        },
      );
      if (res) {
        resTx = res.map((res) => ccc.Transaction.fromBytes(res));
      }
    }

    if (!resTx) {
      const transfer = ccc.Transaction.from(tx ?? {});
      for (const { to, amount } of transfers) {
        transfer.addOutput(
          {
            lock: to,
            type: this.script,
          },
          ccc.numLeToBytes(amount, 16),
        );
      }
      resTx = ssri.ExecutorResponse.new(transfer);
    }
    resTx.res.addCellDeps({
      outPoint: this.code,
      depType: "code",
    });
    return resTx;
  }

  /**
   * Mints new tokens to specified addresses.
   * This method creates new UDT tokens and assigns them to the specified recipients.
   * The minting operation requires appropriate permissions and may be restricted
   * based on the UDT's implementation.
   *
   * @param signer - The signer that will authorize and potentially pay for the transaction
   * @param mints - Array of mint operations to perform
   * @param mints.to - The lock script of the recipient who will receive the minted tokens
   * @param mints.amount - The amount of tokens to mint for this recipient (in smallest unit)
   * @param tx - Optional existing transaction to build upon. If not provided, a new transaction will be created
   * @returns A promise resolving to an ExecutorResponse containing the transaction with mint operations
   *
   * @tag Mutation - This method represents a mutation of the onchain state
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   * const { script: recipientLock } = await ccc.Address.fromString(recipientAddress, signer.client);
   *
   * const mintResponse = await udt.mint(
   *   signer,
   *   [
   *     { to: recipientLock, amount: ccc.fixedPointFrom(1000) }, // Mint 1000 tokens
   *     { to: anotherLock, amount: ccc.fixedPointFrom(500) }     // Mint 500 tokens
   *   ]
   * );
   *
   * // Complete the transaction
   * const tx = mintResponse.res;
   * await tx.completeInputsByCapacity(signer);
   * await tx.completeFee(signer, (tx, capacity) => {
   *   tx.addOutput({ capacity, lock: changeLock });
   *   return 0;
   * });
   *
   * const txHash = await signer.sendTransaction(tx);
   * ```
   *
   * @throws May throw if the signer doesn't have minting permissions or if the UDT doesn't support minting
   */
  async mint(
    signer: ccc.Signer,
    mints: {
      to: ccc.ScriptLike;
      amount: ccc.NumLike;
    }[],
    tx?: ccc.TransactionLike | null,
  ): Promise<ssri.ExecutorResponse<ccc.Transaction>> {
    let resTx;
    if (this.executor) {
      const txReq = ccc.Transaction.from(tx ?? {});
      await txReq.completeInputsAtLeastOne(signer);

      const res = await this.executor.runScriptTry(
        this.code,
        "UDT.mint",
        [
          txReq.toBytes(),
          ccc.ScriptVec.encode(mints.map(({ to }) => to)),
          ccc.mol.Uint128Vec.encode(mints.map(({ amount }) => amount)),
        ],
        {
          script: this.script,
        },
      );
      if (res) {
        resTx = res.map((res) => ccc.Transaction.fromBytes(res));
      }
    }

    if (!resTx) {
      const mint = ccc.Transaction.from(tx ?? {});
      for (const { to, amount } of mints) {
        mint.addOutput(
          {
            lock: to,
            type: this.script,
          },
          ccc.numLeToBytes(amount),
        );
      }
      resTx = ssri.ExecutorResponse.new(mint);
    }
    resTx.res.addCellDeps({
      outPoint: this.code,
      depType: "code",
    });
    return resTx;
  }

  isUdtCell(cellOutputLike: ccc.CellOutputLike, outputData: ccc.HexLike) {
    return (
      (ccc.CellOutput.from(cellOutputLike).type?.eq(this.script) ?? false) &&
      ccc.bytesFrom(outputData).length >= 16
    );
  }

  /**
   * Calculates the total UDT balance from all inputs in a transaction.
   * This method examines each input cell and sums up the UDT amounts
   * for cells that have this UDT's type script.
   *
   * @param txLike - The transaction to analyze
   * @param client - The client to fetch input cell data
   * @returns A promise resolving to the total UDT balance from all inputs
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   * const tx = ccc.Transaction.from(existingTransaction);
   *
   * const inputBalance = await udt.getInputsBalance(tx, client);
   * console.log(`Total UDT input balance: ${inputBalance}`);
   * ```
   *
   * @remarks
   * This method only counts inputs that have the same type script as this UDT instance.
   * Inputs without a type script or with different type scripts are ignored.
   */
  async getInputsBalance(
    txLike: ccc.TransactionLike,
    client: ccc.Client,
  ): Promise<ccc.Num> {
    const tx = ccc.Transaction.from(txLike);
    return ccc.reduceAsync(
      tx.inputs,
      async (acc, input) => {
        const { cellOutput, outputData } = await input.getCell(client);
        if (!this.isUdtCell(cellOutput, outputData)) {
          return;
        }

        return acc + ccc.udtBalanceFrom(outputData);
      },
      ccc.numFrom(0),
    );
  }

  /**
   * Calculates the total UDT balance from all outputs in a transaction.
   * This method examines each output cell and sums up the UDT amounts
   * for cells that have this UDT's type script.
   *
   * @param txLike - The transaction to analyze
   * @returns The total UDT balance from all outputs
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   * const tx = ccc.Transaction.from({
   *   outputs: [
   *     { lock: recipientLock, type: udt.script },
   *     { lock: changeLock, type: udt.script }
   *   ],
   *   outputsData: [
   *     ccc.numLeToBytes(1000, 16), // 1000 UDT to recipient
   *     ccc.numLeToBytes(500, 16)   // 500 UDT as change
   *   ]
   * });
   *
   * const outputBalance = udt.getOutputsBalance(tx);
   * console.log(`Total UDT output balance: ${outputBalance}`); // 1500
   * ```
   *
   * @remarks
   * This method only counts outputs that have the same type script as this UDT instance.
   * Outputs without a type script or with different type scripts are ignored.
   * This is a synchronous method since output data is already available in the transaction.
   */
  getOutputsBalance(txLike: ccc.TransactionLike): ccc.Num {
    const tx = ccc.Transaction.from(txLike);
    return tx.outputs.reduce((acc, output, i) => {
      if (!this.isUdtCell(output, tx.outputsData[i])) {
        return acc;
      }

      return acc + ccc.udtBalanceFrom(tx.outputsData[i]);
    }, ccc.numFrom(0));
  }

  async getBalanceBurned(
    txLike: ccc.TransactionLike,
    client: ccc.Client,
  ): Promise<ccc.Num> {
    const tx = ccc.Transaction.from(txLike);
    return (
      (await this.getInputsBalance(tx, client)) - this.getOutputsBalance(tx)
    );
  }

  /**
   * Completes UDT inputs for a transaction to cover the required output balance.
   * This method automatically adds UDT inputs from the signer's available cells
   * to ensure the transaction has sufficient UDT balance to cover all outputs,
   * while also considering the capacity balance for transaction fees.
   *
   * @param txLike - The transaction to complete with UDT inputs
   * @param from - The signer that will provide UDT inputs
   * @param balanceTweak - Optional additional UDT balance to account for (e.g., for extra requirements)
   * @returns A promise resolving to the number of inputs added to the transaction
   *
   * @throws {ErrorTransactionInsufficientCoin} When there are insufficient UDT cells to cover the required balance
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   *
   * // Create a transaction that needs 1000 UDT
   * const tx = ccc.Transaction.from({
   *   outputs: [
   *     { lock: recipientLock, type: udt.script }
   *   ],
   *   outputsData: [ccc.numLeToBytes(1000, 16)]
   * });
   *
   * // Add UDT inputs to cover the 1000 UDT requirement
   * const addedInputs = await udt.completeInputs(tx, signer);
   * console.log(`Added ${addedInputs} UDT inputs to the transaction`);
   * ```
   *
   * @remarks
   * This method implements smart input selection with dual balance consideration:
   * - Calculates the required UDT balance (outputs + balanceTweak)
   * - Considers both UDT balance and capacity balance for optimal selection
   * - If existing inputs already cover the required UDT balance and capacity is non-negative, no new inputs are added
   * - Adds inputs until both UDT balance requirement is met and capacity balance is non-negative
   * - Uses accumulator pattern to track both UDT balance and capacity during input selection
   * - Throws an error if the signer doesn't have sufficient UDT balance
   */
  async completeInputs(
    txLike: ccc.TransactionLike,
    from: ccc.Signer,
    balanceTweak?: ccc.NumLike,
    capacityTweak?: ccc.NumLike,
  ): Promise<number> {
    const tx = ccc.Transaction.from(txLike);
    const balanceBurned =
      (await this.getBalanceBurned(tx, from.client)) -
      ccc.numFrom(balanceTweak ?? 0);
    const capacityBurned =
      (await tx.getFee(from.client)) - ccc.numFrom(capacityTweak ?? 0);

    if (balanceBurned >= ccc.Zero && capacityBurned >= ccc.Zero) {
      return 0;
    }

    const { addedCount, accumulated } = await tx.completeInputs(
      from,
      {
        script: this.script,
        outputDataLenRange: [16, ccc.numFrom("0xffffffff")],
      },
      ([balanceAcc, capacityAcc], { cellOutput: { capacity }, outputData }) => {
        const balance = ccc.udtBalanceFrom(outputData);
        const balanceBurned = balanceAcc + balance;
        const capacityBurned = capacityAcc + capacity;

        // Try to provide enough capacity with UDT cells to avoid extra occupation
        return balanceBurned >= ccc.Zero && capacityBurned >= ccc.Zero
          ? undefined
          : [balanceBurned, capacityBurned];
      },
      [balanceBurned, capacityBurned],
    );

    if (accumulated === undefined || accumulated[0] >= ccc.Zero) {
      return addedCount;
    }

    throw new ccc.ErrorTransactionInsufficientCoin(
      -accumulated[0],
      this.script,
    );
  }

  async complete(
    txLike: ccc.TransactionLike,
    signer: ccc.Signer,
    change: (
      tx: ccc.Transaction,
      balance: ccc.Num,
      shouldModify: boolean,
    ) => Promise<ccc.NumLike> | ccc.NumLike,
    options?: { shouldAddInputs?: boolean },
  ): Promise<ccc.Transaction> {
    const tx = ccc.Transaction.from(txLike);

    /* === Figure out the balance to change === */
    if (options?.shouldAddInputs ?? true) {
      await this.completeInputs(tx, signer);
    }

    const balanceBurned = await this.getBalanceBurned(tx, signer.client);

    if (balanceBurned < ccc.Zero) {
      throw new ccc.ErrorTransactionInsufficientCoin(
        -balanceBurned,
        this.script,
      );
    } else if (balanceBurned === ccc.Zero) {
      return tx;
    }
    /* === Some balance need to change === */

    if (!(options?.shouldAddInputs ?? true)) {
      await Promise.resolve(change(tx, balanceBurned, true));
      return tx;
    }

    const extraCapacity = ccc.numFrom(
      await Promise.resolve(change(tx, balanceBurned, false)),
    ); // Extra capacity introduced by change cell
    await this.completeInputs(tx, signer, ccc.Zero, extraCapacity);

    const balanceToChange = await this.getBalanceBurned(tx, signer.client);
    await Promise.resolve(change(tx, balanceToChange, true));

    return tx;
  }

  async completeChangeToOutput(
    txLike: ccc.TransactionLike,
    signer: ccc.Signer,
    indexLike: ccc.NumLike,
    options?: { shouldAddInputs?: boolean },
  ) {
    const tx = ccc.Transaction.from(txLike);
    const index = Number(ccc.numFrom(indexLike));
    const outputData = ccc.bytesFrom(tx.outputsData[index]);

    if (!this.isUdtCell(tx.outputs[index], outputData)) {
      throw new Error("Change output must be a UDT cell");
    }

    return this.complete(
      tx,
      signer,
      (tx, balance, shouldModify) => {
        if (shouldModify) {
          const balanceData = ccc.numLeToBytes(
            ccc.udtBalanceFrom(outputData) + balance,
            16,
          );

          tx.outputsData[index] = ccc.hexFrom(
            ccc.bytesConcatTo([], balanceData, outputData.slice(16)),
          );
        }

        return 0;
      },
      options,
    );
  }

  /**
   * Completes a UDT transaction by adding necessary inputs and handling change.
   * This method automatically adds UDT inputs to cover the required output amounts
   * and creates a change output if there's excess UDT balance.
   *
   * @param txLike - The transaction to complete, containing UDT outputs
   * @param signer - The signer that will provide UDT inputs
   * @param change - The lock script where any excess UDT balance should be sent as change
   * @param options - Optional configuration for the completion process
   * @param options.shouldAddInputs - Whether to automatically add inputs. Defaults to true
   * @returns A promise resolving to the completed transaction with inputs and change output added
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   *
   * // Create a transaction with UDT outputs
   * const tx = ccc.Transaction.from({
   *   outputs: [
   *     { lock: recipientLock, type: udt.script }
   *   ],
   *   outputsData: [ccc.numLeToBytes(1000, 16)] // Send 1000 UDT
   * });
   *
   * // Complete with change going to sender's address
   * const { script: changeLock } = await signer.getRecommendedAddressObj();
   * const completedTx = await udt.completeChangeToLock(tx, signer, changeLock);
   *
   * // The transaction now has:
   * // - Sufficient UDT inputs to cover the 1000 UDT output
   * // - A change output if there was excess UDT balance
   * ```
   *
   * @remarks
   * This method performs the following operations:
   * 1. Adds UDT inputs using `completeInputsByUdt`
   * 2. Calculates the difference between input and output UDT balances
   * 3. Creates a change output if there's excess UDT balance
   */
  async completeChangeToLock(
    tx: ccc.TransactionLike,
    signer: ccc.Signer,
    changeLike: ccc.ScriptLike,
    options?: { shouldAddInputs?: boolean },
  ) {
    const change = ccc.Script.from(changeLike);

    return this.complete(
      tx,
      signer,
      (tx, balance, shouldModify) => {
        const balanceData = ccc.numLeToBytes(balance, 16);
        const changeOutput = ccc.CellOutput.from(
          { lock: change, type: this.script },
          balanceData,
        );
        if (shouldModify) {
          tx.addOutput(changeOutput, balanceData);
        }

        return changeOutput.capacity;
      },
      options,
    );
  }

  /**
   * Completes a UDT transaction using the signer's recommended address for change.
   * This is a convenience method that automatically uses the signer's recommended
   * address as the change destination, making it easier to complete UDT transactions
   * without manually specifying a change address.
   *
   * @param tx - The transaction to complete, containing UDT outputs
   * @param from - The signer that will provide UDT inputs and receive change
   * @param options - Optional configuration for the completion process
   * @param options.shouldAddInputs - Whether to automatically add inputs. Defaults to true
   * @returns A promise resolving to the completed transaction with inputs and change output added
   *
   * @example
   * ```typescript
   * const udt = new Udt(codeOutPoint, scriptConfig);
   *
   * // Create a transfer transaction
   * const transferResponse = await udt.transfer(
   *   signer,
   *   [{ to: recipientLock, amount: 1000 }]
   * );
   *
   * // Complete the transaction (change will go to signer's address)
   * const completedTx = await udt.completeBy(transferResponse.res, signer);
   *
   * // Add capacity inputs and fee
   * await completedTx.completeInputsByCapacity(signer);
   * await completedTx.completeFee(signer, (tx, capacity) => {
   *   tx.addOutput({ capacity, lock: changeLock });
   *   return 0;
   * });
   *
   * const txHash = await signer.sendTransaction(completedTx);
   * ```
   *
   * @see {@link completeChangeToLock} for more control over the change destination
   */
  async completeBy(
    tx: ccc.TransactionLike,
    from: ccc.Signer,
    options?: { shouldAddInputs?: boolean },
  ) {
    const { script } = await from.getRecommendedAddressObj();

    return this.completeChangeToLock(tx, from, script, options);
  }
}
